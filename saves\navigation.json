{"error": false, "size": [1920, 1080], "name": "TLH", "version": "1.0", "location": {"label": {"relationship": ["game/relationship.rpy", 1], "start": ["game/script.rpy", 13], "anomaly": ["game/script.rpy", 47], "block": ["game/script.rpy", 51], "mission": ["game/script.rpy", 55], "inventory": ["game/script.rpy", 59]}, "define": {"config.check_conflicting_properties": ["game/gui.rpy", 15], "gui.accent_color": ["game/gui.rpy", 28], "gui.idle_color": ["game/gui.rpy", 31], "gui.idle_small_color": ["game/gui.rpy", 35], "gui.hover_color": ["game/gui.rpy", 38], "gui.selected_color": ["game/gui.rpy", 42], "gui.insensitive_color": ["game/gui.rpy", 45], "gui.muted_color": ["game/gui.rpy", 49], "gui.hover_muted_color": ["game/gui.rpy", 50], "gui.text_color": ["game/gui.rpy", 53], "gui.interface_text_color": ["game/gui.rpy", 54], "gui.text_font": ["game/gui.rpy", 60], "gui.name_text_font": ["game/gui.rpy", 63], "gui.interface_text_font": ["game/gui.rpy", 66], "gui.text_size": ["game/gui.rpy", 69], "gui.name_text_size": ["game/gui.rpy", 72], "gui.interface_text_size": ["game/gui.rpy", 75], "gui.label_text_size": ["game/gui.rpy", 78], "gui.notify_text_size": ["game/gui.rpy", 81], "gui.title_text_size": ["game/gui.rpy", 84], "gui.main_menu_background": ["game/gui.rpy", 90], "gui.game_menu_background": ["game/gui.rpy", 91], "gui.textbox_height": ["game/gui.rpy", 100], "gui.textbox_yalign": ["game/gui.rpy", 104], "gui.name_xpos": ["game/gui.rpy", 109], "gui.name_ypos": ["game/gui.rpy", 110], "gui.name_xalign": ["game/gui.rpy", 114], "gui.namebox_width": ["game/gui.rpy", 118], "gui.namebox_height": ["game/gui.rpy", 119], "gui.namebox_borders": ["game/gui.rpy", 123], "gui.namebox_tile": ["game/gui.rpy", 127], "gui.dialogue_xpos": ["game/gui.rpy", 133], "gui.dialogue_ypos": ["game/gui.rpy", 134], "gui.dialogue_width": ["game/gui.rpy", 137], "gui.dialogue_text_xalign": ["game/gui.rpy", 141], "gui.button_width": ["game/gui.rpy", 150], "gui.button_height": ["game/gui.rpy", 151], "gui.button_borders": ["game/gui.rpy", 154], "gui.button_tile": ["game/gui.rpy", 158], "gui.button_text_font": ["game/gui.rpy", 161], "gui.button_text_size": ["game/gui.rpy", 164], "gui.button_text_idle_color": ["game/gui.rpy", 167], "gui.button_text_hover_color": ["game/gui.rpy", 168], "gui.button_text_selected_color": ["game/gui.rpy", 169], "gui.button_text_insensitive_color": ["game/gui.rpy", 170], "gui.button_text_xalign": ["game/gui.rpy", 174], "gui.radio_button_borders": ["game/gui.rpy", 183], "gui.check_button_borders": ["game/gui.rpy", 185], "gui.confirm_button_text_xalign": ["game/gui.rpy", 187], "gui.page_button_borders": ["game/gui.rpy", 189], "gui.quick_button_borders": ["game/gui.rpy", 191], "gui.quick_button_text_size": ["game/gui.rpy", 192], "gui.quick_button_text_idle_color": ["game/gui.rpy", 193], "gui.quick_button_text_selected_color": ["game/gui.rpy", 194], "gui.choice_button_width": ["game/gui.rpy", 207], "gui.choice_button_height": ["game/gui.rpy", 208], "gui.choice_button_tile": ["game/gui.rpy", 209], "gui.choice_button_borders": ["game/gui.rpy", 210], "gui.choice_button_text_font": ["game/gui.rpy", 211], "gui.choice_button_text_size": ["game/gui.rpy", 212], "gui.choice_button_text_xalign": ["game/gui.rpy", 213], "gui.choice_button_text_idle_color": ["game/gui.rpy", 214], "gui.choice_button_text_hover_color": ["game/gui.rpy", 215], "gui.choice_button_text_insensitive_color": ["game/gui.rpy", 216], "gui.slot_button_width": ["game/gui.rpy", 226], "gui.slot_button_height": ["game/gui.rpy", 227], "gui.slot_button_borders": ["game/gui.rpy", 228], "gui.slot_button_text_size": ["game/gui.rpy", 229], "gui.slot_button_text_xalign": ["game/gui.rpy", 230], "gui.slot_button_text_idle_color": ["game/gui.rpy", 231], "gui.slot_button_text_selected_idle_color": ["game/gui.rpy", 232], "gui.slot_button_text_selected_hover_color": ["game/gui.rpy", 233], "config.thumbnail_width": ["game/gui.rpy", 236], "config.thumbnail_height": ["game/gui.rpy", 237], "gui.file_slot_cols": ["game/gui.rpy", 240], "gui.file_slot_rows": ["game/gui.rpy", 241], "gui.navigation_xpos": ["game/gui.rpy", 251], "gui.skip_ypos": ["game/gui.rpy", 254], "gui.notify_ypos": ["game/gui.rpy", 257], "gui.choice_spacing": ["game/gui.rpy", 260], "gui.navigation_spacing": ["game/gui.rpy", 263], "gui.pref_spacing": ["game/gui.rpy", 266], "gui.pref_button_spacing": ["game/gui.rpy", 269], "gui.page_spacing": ["game/gui.rpy", 272], "gui.slot_spacing": ["game/gui.rpy", 275], "gui.main_menu_text_xalign": ["game/gui.rpy", 278], "gui.frame_borders": ["game/gui.rpy", 287], "gui.confirm_frame_borders": ["game/gui.rpy", 290], "gui.skip_frame_borders": ["game/gui.rpy", 293], "gui.notify_frame_borders": ["game/gui.rpy", 296], "gui.frame_tile": ["game/gui.rpy", 299], "gui.bar_size": ["game/gui.rpy", 311], "gui.scrollbar_size": ["game/gui.rpy", 312], "gui.slider_size": ["game/gui.rpy", 313], "gui.bar_tile": ["game/gui.rpy", 316], "gui.scrollbar_tile": ["game/gui.rpy", 317], "gui.slider_tile": ["game/gui.rpy", 318], "gui.bar_borders": ["game/gui.rpy", 321], "gui.scrollbar_borders": ["game/gui.rpy", 322], "gui.slider_borders": ["game/gui.rpy", 323], "gui.vbar_borders": ["game/gui.rpy", 326], "gui.vscrollbar_borders": ["game/gui.rpy", 327], "gui.vslider_borders": ["game/gui.rpy", 328], "gui.unscrollable": ["game/gui.rpy", 332], "config.history_length": ["game/gui.rpy", 340], "gui.history_height": ["game/gui.rpy", 344], "gui.history_spacing": ["game/gui.rpy", 347], "gui.history_name_xpos": ["game/gui.rpy", 351], "gui.history_name_ypos": ["game/gui.rpy", 352], "gui.history_name_width": ["game/gui.rpy", 353], "gui.history_name_xalign": ["game/gui.rpy", 354], "gui.history_text_xpos": ["game/gui.rpy", 357], "gui.history_text_ypos": ["game/gui.rpy", 358], "gui.history_text_width": ["game/gui.rpy", 359], "gui.history_text_xalign": ["game/gui.rpy", 360], "gui.nvl_borders": ["game/gui.rpy", 368], "gui.nvl_list_length": ["game/gui.rpy", 372], "gui.nvl_height": ["game/gui.rpy", 376], "gui.nvl_spacing": ["game/gui.rpy", 380], "gui.nvl_name_xpos": ["game/gui.rpy", 384], "gui.nvl_name_ypos": ["game/gui.rpy", 385], "gui.nvl_name_width": ["game/gui.rpy", 386], "gui.nvl_name_xalign": ["game/gui.rpy", 387], "gui.nvl_text_xpos": ["game/gui.rpy", 390], "gui.nvl_text_ypos": ["game/gui.rpy", 391], "gui.nvl_text_width": ["game/gui.rpy", 392], "gui.nvl_text_xalign": ["game/gui.rpy", 393], "gui.nvl_thought_xpos": ["game/gui.rpy", 397], "gui.nvl_thought_ypos": ["game/gui.rpy", 398], "gui.nvl_thought_width": ["game/gui.rpy", 399], "gui.nvl_thought_xalign": ["game/gui.rpy", 400], "gui.nvl_button_xpos": ["game/gui.rpy", 403], "gui.nvl_button_xalign": ["game/gui.rpy", 404], "gui.language": ["game/gui.rpy", 413], "quick_menu": ["game/screens.rpy", 267], "gui.history_allow_tags": ["game/screens.rpy", 835], "config.nvl_list_length": ["game/screens.rpy", 1260], "bubble.frame": ["game/screens.rpy", 1369], "bubble.thoughtframe": ["game/screens.rpy", 1370], "bubble.properties": ["game/screens.rpy", 1372], "bubble.expand_area": ["game/screens.rpy", 1398], "gui.toggle_mute": ["game/assets/ui/ui.rpy", 7], "config.name": ["game/options.rpy", 15], "gui.show_name": ["game/options.rpy", 21], "config.version": ["game/options.rpy", 26], "gui.about": ["game/options.rpy", 32], "build.name": ["game/options.rpy", 40], "config.has_sound": ["game/options.rpy", 49], "config.has_music": ["game/options.rpy", 50], "config.has_voice": ["game/options.rpy", 51], "config.enter_transition": ["game/options.rpy", 76], "config.exit_transition": ["game/options.rpy", 77], "config.intra_transition": ["game/options.rpy", 82], "config.after_load_transition": ["game/options.rpy", 87], "config.end_game_transition": ["game/options.rpy", 92], "config.window": ["game/options.rpy", 109], "config.window_show_transition": ["game/options.rpy", 114], "config.window_hide_transition": ["game/options.rpy", 115], "config.save_directory": ["game/options.rpy", 146], "config.window_icon": ["game/options.rpy", 153], "names": ["game/relationship.rpy", 23], "names_big": ["game/relationship.rpy", 24], "love_current": ["game/relationship.rpy", 25], "love_max": ["game/relationship.rpy", 26], "ntr_current": ["game/relationship.rpy", 27], "ntr_max": ["game/relationship.rpy", 28], "statusRange": ["game/relationship.rpy", 29], "playerChose": ["game/relationship.rpy", 30], "e": ["game/script.rpy", 4]}, "screen": {"say": ["game/screens.rpy", 97], "input": ["game/screens.rpy", 175], "choice": ["game/screens.rpy", 208], "quick_menu": ["game/screens.rpy", 1419], "navigation": ["game/screens.rpy", 302], "game_menu": ["game/screens.rpy", 419], "about": ["game/screens.rpy", 533], "history": ["game/screens.rpy", 793], "help": ["game/screens.rpy", 883], "keyboard_help": ["game/screens.rpy", 912], "mouse_help": ["game/screens.rpy", 963], "gamepad_help": ["game/screens.rpy", 986], "confirm": ["game/screens.rpy", 1051], "skip_indicator": ["game/screens.rpy", 1114], "notify": ["game/screens.rpy", 1169], "nvl": ["game/screens.rpy", 1208], "nvl_dialogue": ["game/screens.rpy", 1240], "bubble": ["game/screens.rpy", 1327], "achievements": ["game/assets/gallery/achievements/achievements.rpy", 24], "bonus": ["game/assets/gallery/bonus.rpy", 24], "nicole": ["game/assets/gallery/characters/nicole.rpy", 11], "codex": ["game/assets/gallery/codex.rpy", 24], "main": ["game/assets/gallery/main.rpy", 29], "replay": ["game/assets/gallery/replay.rpy", 24], "main_menu": ["game/assets/menu/menu.rpy", 64], "save": ["game/assets/save-load/save-load.rpy", 114], "load": ["game/assets/save-load/save-load.rpy", 202], "file_slots": ["game/assets/save-load/save-load.rpy", 291], "preferences": ["game/assets/settings/settings.rpy", 34], "ui": ["game/assets/ui/ui.rpy", 81], "top": ["game/assets/ui/ui.rpy", 219], "block": ["game/block.rpy", 1], "anomaly": ["game/commision.rpy", 56], "confirm_consume": ["game/inventory.rpy", 47], "inventory": ["game/inventory.rpy", 68], "debug_inventory": ["game/inventory.rpy", 133], "mission": ["game/mission.rpy", 187], "quest_content_section": ["game/mission.rpy", 473], "mission_tab_content_modern": ["game/mission.rpy", 623], "relationship": ["game/relationship.rpy", 32], "ui_button_overlay": ["game/script.rpy", 16]}, "transform": {"nav_button_state": ["game/screens.rpy", 288], "nav_return_state": ["game/screens.rpy", 295], "delayed_blink": ["game/screens.rpy", 1132], "notify_appear": ["game/screens.rpy", 1180], "achievements_appear": ["game/assets/gallery/achievements/achievements.rpy", 2], "achievements_hover_effect": ["game/assets/gallery/achievements/achievements.rpy", 11], "hover_with_ease": ["game/assets/gallery/replay.rpy", 17], "bonus_appear": ["game/assets/gallery/bonus.rpy", 2], "bonus_hover_effect": ["game/assets/gallery/bonus.rpy", 11], "nicole_appear": ["game/assets/gallery/characters/nicole.rpy", 2], "codex_appear": ["game/assets/gallery/codex.rpy", 2], "codex_hover_effect": ["game/assets/gallery/codex.rpy", 11], "gallery_button_appear": ["game/assets/gallery/main.rpy", 7], "hover_effect": ["game/assets/gallery/main.rpy", 16], "gallery_button_hover": ["game/assets/gallery/main.rpy", 22], "replay_appear": ["game/assets/gallery/replay.rpy", 2], "replay_hover_effect": ["game/assets/gallery/replay.rpy", 11], "parallax_bg": ["game/assets/ui/ui.rpy", 40], "button_alpha": ["game/assets/menu/menu.rpy", 22], "slide_from_left": ["game/assets/menu/menu.rpy", 28], "slide_from_right": ["game/assets/menu/menu.rpy", 32], "slide_from_bottom": ["game/assets/menu/menu.rpy", 36], "logo_appear": ["game/assets/menu/menu.rpy", 41], "bg_show": ["game/assets/menu/menu.rpy", 49], "bg_hide": ["game/assets/menu/menu.rpy", 53], "dissolve_bg": ["game/assets/menu/menu.rpy", 57], "sizeslot": ["game/assets/save-load/save-load.rpy", 23], "sizes": ["game/assets/save-load/save-load.rpy", 30], "slot_appear": ["game/assets/save-load/save-load.rpy", 38], "fade_up": ["game/assets/save-load/save-load.rpy", 48], "pages_appear": ["game/assets/save-load/save-load.rpy", 57], "slot_hover": ["game/assets/save-load/save-load.rpy", 89], "slot_highlight": ["game/assets/save-load/save-load.rpy", 81], "arrow_button": ["game/assets/save-load/save-load.rpy", 106], "settings_frame_appear": ["game/assets/settings/settings.rpy", 21], "button_hover": ["game/assets/settings/settings.rpy", 28], "icon_transition": ["game/assets/ui/ui.rpy", 10], "time_switch": ["game/assets/ui/ui.rpy", 19], "active_button": ["game/assets/ui/ui.rpy", 49], "inactive_button": ["game/assets/ui/ui.rpy", 56], "blink": ["game/commision.rpy", 20], "abandon_hover_alpha": ["game/commision.rpy", 28], "text_alpha_controlled": ["game/commision.rpy", 34], "dissolve_fade": ["game/commision.rpy", 42], "dissolve_fade_static": ["game/commision.rpy", 45], "dissolve_fade_out": ["game/commision.rpy", 49], "quest_expand": ["game/mission.rpy", 173], "quest_collapse": ["game/mission.rpy", 177], "quest_button_hover": ["game/mission.rpy", 181], "semi_transparent": ["game/relationship.rpy", 5], "slide_from_right_return": ["game/relationship.rpy", 8], "dissolve_in": ["game/relationship.rpy", 12], "dissolve_out": ["game/relationship.rpy", 16], "flip_horizontal": ["game/relationship.rpy", 20]}, "callable": {"touch": ["game/gui.rpy", 425], "small": ["game/gui.rpy", 432], "parallax_effect": ["game/assets/ui/ui.rpy", 31], "SlotStates.__init__": ["game/assets/save-load/save-load.rpy", 9], "SlotStates.set_hover": ["game/assets/save-load/save-load.rpy", 12], "SlotStates.get_hover": ["game/assets/save-load/save-load.rpy", 15], "SlotStates.clear_all": ["game/assets/save-load/save-load.rpy", 18], "switch_language": ["game/assets/settings/settings.rpy", 5], "toggle_skip_setting": ["game/assets/settings/settings.rpy", 11], "toggle_mission": ["game/assets/ui/ui.rpy", 213], "consume_item": ["game/inventory.rpy", 17], "add_item": ["game/inventory.rpy", 30], "remove_item": ["game/inventory.rpy", 37], "toggle_mission_expansion": ["game/mission.rpy", 60], "change_mission_status": ["game/mission.rpy", 67], "get_missions_by_status": ["game/mission.rpy", 77], "get_active_missions": ["game/mission.rpy", 81], "get_outgoing_missions": ["game/mission.rpy", 85], "toggle_debug_mode": ["game/mission.rpy", 92], "debug_set_mission_status": ["game/mission.rpy", 98], "debug_complete_mission": ["game/mission.rpy", 109], "debug_activate_mission": ["game/mission.rpy", 113], "debug_fail_mission": ["game/mission.rpy", 117], "debug_reset_mission": ["game/mission.rpy", 121], "debug_complete_all_main": ["game/mission.rpy", 125], "debug_complete_all_side": ["game/mission.rpy", 131], "debug_reset_all_missions": ["game/mission.rpy", 137], "debug_add_main_mission": ["game/mission.rpy", 146], "debug_add_side_mission": ["game/mission.rpy", 160]}}, "build": {"directory_name": "TLH-1.0", "executable_name": "TLH", "include_update": false, "packages": [{"name": "gameonly", "formats": ["null"], "file_lists": ["all"], "description": "Game-Only Update for Mobile", "update": true, "dlc": false, "hidden": true}, {"name": "pc", "formats": ["zip"], "file_lists": ["windows", "linux", "renpy", "all"], "description": "PC: Windows and Linux", "update": true, "dlc": false, "hidden": false}, {"name": "linux", "formats": ["tar.bz2"], "file_lists": ["linux", "linux_arm", "renpy", "all"], "description": "Linux", "update": true, "dlc": false, "hidden": false}, {"name": "mac", "formats": ["app-zip", "app-dmg"], "file_lists": ["mac", "renpy", "all"], "description": "Macintosh", "update": true, "dlc": false, "hidden": false}, {"name": "win", "formats": ["zip"], "file_lists": ["windows", "renpy", "all"], "description": "Windows", "update": true, "dlc": false, "hidden": false}, {"name": "market", "formats": ["bare-zip"], "file_lists": ["windows", "linux", "mac", "renpy", "all"], "description": "Windows, Mac, Linux for Markets", "update": true, "dlc": false, "hidden": false}, {"name": "steam", "formats": ["zip"], "file_lists": ["windows", "linux", "mac", "renpy", "all"], "description": "steam", "update": true, "dlc": false, "hidden": true}, {"name": "android", "formats": ["directory"], "file_lists": ["android", "all"], "description": "android", "update": false, "dlc": true, "hidden": true}, {"name": "ios", "formats": ["directory"], "file_lists": ["ios", "all"], "description": "ios", "update": false, "dlc": true, "hidden": true}, {"name": "web", "formats": ["zip"], "file_lists": ["web", "renpy", "all"], "description": "web", "update": false, "dlc": true, "hidden": true}], "archives": [["archive", ["all"]]], "documentation_patterns": ["*.html", "*.txt"], "base_patterns": [["*.py", null], ["*.sh", null], ["*.app/", null], ["*.dll", null], ["*.manifest", null], ["*.keystore", null], ["**.rpe.py", null], ["update.pem", null], ["lib/", null], ["renpy/", null], ["update/", null], ["common/", null], ["update/", null], ["old-game/", null], ["base/", null], ["icon.ico", null], ["icon.icns", null], ["project.json", null], ["log.txt", null], ["errors.txt", null], ["traceback.txt", null], ["image_cache.txt", null], ["text_overflow.txt", null], ["dialogue.txt", null], ["dialogue.tab", null], ["profile_screen.txt", null], ["files.txt", null], ["memory.txt", null], ["tmp/", null], ["game/saves/", null], ["game/bytecode.rpyb", null], ["archived/", null], ["launcherinfo.py", null], ["android.txt", null], ["game/presplash*.*", ["all"]], ["android.json", ["android"]], [".android.json", ["android"]], ["android-*.png", ["android"]], ["android-*.jpg", ["android"]], ["ouya_icon.png", null], ["ios-presplash.*", ["ios"]], ["ios-launchimage.png", null], ["ios-icon.png", null], ["web-presplash.png", ["web"]], ["web-presplash.jpg", ["web"]], ["web-presplash.webp", ["web"]], ["web-icon.png", ["web"]], ["progressive_download.txt", ["web"]], ["steam_appid.txt", null], ["game/cache/bytecode-39.rpyb", ["all"]], ["game/cache/bytecode-311.rpyb", ["web"]], ["game/cache/bytecode-*.rpyb", null], ["game/cache/build_info.json", null], ["game/cache/build_time.txt", null], ["**~", null], ["**.bak", null], ["**/.**", null], ["**/#**", null], ["**/thumbs.db", null], [".*", null], ["**", ["all"]]], "renpy_patterns": [["renpy/common/_compat/**", null], ["renpy/common/_roundrect/**", null], ["renpy/common/_outline/**", null], ["renpy/common/_theme**", null], ["renpy/**__pycache__/**.cpython-39.pyc", ["all"]], ["renpy/**__pycache__", ["all"]], ["**~", null], ["**/#*", null], ["**/.*", null], ["**.old", null], ["**.new", null], ["**.rpa", null], ["**.rpe", null], ["**.rpe.py", null], ["**/steam_appid.txt", null], ["renpy.py", ["all"]], ["renpy/", ["all"]], ["renpy/**.py", ["renpy"]], ["renpy/**.pxd", null], ["renpy/**.pxi", null], ["renpy/**.pyx", null], ["renpy/**.pyc", null], ["renpy/**.pyo", null], ["renpy/common/", ["all"]], ["renpy/common/_compat/**", ["renpy"]], ["renpy/common/**.rpy", ["renpy"]], ["renpy/common/**.rpym", ["renpy"]], ["renpy/common/_compat/**", ["renpy"]], ["renpy/common/**", ["all"]], ["renpy/**", ["all"]], ["lib/*/renpy", null], ["lib/*/renpy.exe", null], ["lib/*/pythonw.exe", null], ["lib/py2-*/", null], ["lib/py*-windows-i686/**", ["windows_i686"]], ["lib/py*-windows-x86_64/**", ["windows"]], ["lib/py*-linux-i686/**", ["linux_i686"]], ["lib/py*-linux-aarch64/**", ["linux_arm"]], ["lib/py*-linux-armv7l/**", ["linux_arm"]], ["lib/py*-linux-*/**", ["linux"]], ["lib/py*-mac-*/**", ["mac"]], ["lib/python2.*/**", null], ["lib/**", ["windows", "linux", "mac", "android", "ios"]], ["renpy.sh", ["linux", "mac"]]], "xbit_patterns": ["**.sh", "lib/py*-linux-*/*", "lib/py*-mac-*/*", "**.app/Contents/MacOS/*"], "version": "1.0", "display_name": "TLH", "exclude_empty_directories": true, "allow_integrated_gpu": true, "renpy": false, "script_version": true, "destination": "TLH-1.0-dists", "itch_channels": {"*-all.zip": "win-osx-linux", "*-market.zip": "win-osx-linux", "*-pc.zip": "win-linux", "*-win.zip": "win", "*-mac.zip": "osx", "*-linux.tar.bz2": "linux", "*-release.apk": "android"}, "mac_info_plist": {}, "merge": [["linux_i686", "linux"], ["windows_i686", "windows"]], "include_i686": true, "change_icon_i686": true, "android_permissions": [], "_sdk_fonts": false, "update_formats": ["rpu"], "info": {"info": {}, "time": 1749290507.3100507, "name": "TLH", "version": "1.0"}}}